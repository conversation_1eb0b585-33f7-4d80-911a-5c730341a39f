/* Reset all margins and padding */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* DIN A4 Print-specific styles */
@media print {
    @page {
        size: A4;
        margin: 15mm 15mm 15mm 15mm;
    }

    html, body {
        width: 210mm;
        height: 297mm;
        margin: 0;
        padding: 0;
        background: white;
        font-size: 12pt;
        line-height: 1.4;
    }

    .container {
        width: 100%;
        max-width: none;
        margin: 0;
        padding: 0;
        box-shadow: none;
        background: white;
        page-break-inside: avoid;
    }

    .sidebar {
        width: 30%;
        background-color: #e6e6e6 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .main-content {
        width: 70%;
    }

    /* Prevent page breaks in important sections */
    .timeline-item {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    .section {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    /* Ensure profile image prints correctly */
    .profile-image img {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    /* Optimize font sizes for print */
    h1 {
        font-size: 24pt !important;
    }

    h2 {
        font-size: 14pt !important;
        margin-top: 16pt !important;
        margin-bottom: 8pt !important;
    }

    h3 {
        font-size: 12pt !important;
    }

    .contact-item p, .skills-section li {
        font-size: 10pt !important;
    }

    .timeline-content li, .timeline-content p {
        font-size: 11pt !important;
    }

    .date {
        font-size: 10pt !important;
    }

    /* Ensure proper spacing for print */
    .sidebar {
        padding: 15pt !important;
    }

    .main-content {
        padding: 20pt !important;
    }
}

/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, Helvetica, sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
}

/* Container optimized for A4 dimensions */
.container {
    display: flex;
    width: 210mm;
    max-width: 210mm;
    min-height: 297mm;
    margin: 0 auto;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    padding: 0;
}

h1, h2, h3 {
    color: #333;
    margin-bottom: 10px;
}

h1 {
    font-size: 28px;
    letter-spacing: 1px;
}

h2 {
    font-size: 18px;
    border-bottom: 1px solid #333;
    padding-bottom: 5px;
    margin-top: 20px;
    margin-bottom: 15px;
}

h3 {
    font-size: 16px;
    font-weight: bold;
}

ul {
    list-style-type: none;
    margin-left: 5px;
}

li {
    margin-bottom: 5px;
}

/* Sidebar Styles */
.sidebar {
    width: 30%;
    background-color: #e6e6e6;
    padding: 20px;
    color: #333;
}

.profile-image-container {
    text-align: center;
    margin-bottom: 20px;
}

.profile-image {
    width: 170px;
    height: 200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.contact-section, .skills-section {
    margin-bottom: 25px;
}

.contact-item {
    display: flex;
    margin-bottom: 10px;
    align-items: flex-start;
}

.contact-item i {
    width: 20px;
    margin-right: 10px;
    color: #555;
}

.contact-item p {
    flex: 1;
    font-size: 14px;
}

.skills-section ul {
    margin-left: 5px;
}

.skills-section li {
    margin-bottom: 8px;
    font-size: 14px;
}

/* Main Content Styles */
.main-content {
    width: 70%;
    padding: 30px;
}

.header {
    margin-bottom: 30px;
    text-align: left;
}

.header h3 {
    font-weight: normal;
    color: #666;
    margin-top: 5px;
}

.divider {
    height: 2px;
    background-color: #333;
    margin: 10px 0 20px 0;
}

.section {
    margin-bottom: 25px;
}

.profile-summary {
    font-size: 15px;
    line-height: 1.6;
    text-align: justify;
}

/* Timeline Styles */
.timeline-item {
    position: relative;
    padding-left: 25px;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 5px;
    width: 8px;
    height: 8px;
    background-color: #333;
    border-radius: 50%;
}

/* Removed the vertical lines */

.timeline-content {
    margin-bottom: 10px;
    position: relative;
}

.timeline-content h3 {
    margin-bottom: 5px;
    padding-right: 100px; /* Make space for the date */
}

.timeline-content ul {
    margin-bottom: 10px;
}

.timeline-content p {
    font-size: 15px;
    margin-bottom: 5px;
    font-weight: normal;
}

.timeline-content li {
    font-size: 15px;
}

.date {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .sidebar, .main-content {
        width: 100%;
    }

    .sidebar {
        order: 2;
    }

    .main-content {
        order: 1;
    }
}






