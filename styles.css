/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.4;
    color: #333;
    background-color: #f5f5f5;
    padding: 10px;
}

/* Container for A4 format - compact and properly sized */
.container {
    width: 190mm;
    max-width: 190mm;
    min-height: 270mm;
    background: white;
    margin: 0 auto;
    padding: 15mm;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Header with photo and name */
.header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #333;
}

.photo-section {
    margin-right: 15px;
    flex-shrink: 0;
}

.profile-photo {
    width: 70px;
    height: 90px;
    object-fit: cover;
    border: 1px solid #ccc;
}

.name-section h1 {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 6px;
    color: #333;
}

.contact-info p {
    font-size: 11px;
    margin-bottom: 1px;
    color: #666;
}

/* Section styling */
.section {
    margin-bottom: 16px;
}

.section h2 {
    font-size: 13px;
    font-weight: bold;
    text-transform: uppercase;
    margin-bottom: 8px;
    padding-bottom: 2px;
    border-bottom: 1px solid #333;
    color: #333;
}

/* Entry styling */
.entry {
    margin-bottom: 10px;
}

.entry-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 2px;
}

.entry-header h3 {
    font-size: 11px;
    font-weight: bold;
    color: #333;
}

.date {
    font-size: 10px;
    color: #666;
    font-weight: normal;
}

.entry ul {
    list-style: none;
    margin-left: 0;
}

.entry li {
    font-size: 10px;
    margin-bottom: 1px;
    position: relative;
    padding-left: 6px;
}

.entry li:before {
    content: "•";
    position: absolute;
    left: 0;
}

.entry p {
    font-size: 10px;
    margin-bottom: 1px;
}

/* Skills sections */
.section ul {
    list-style: none;
}

.section > ul li {
    font-size: 10px;
    margin-bottom: 2px;
    position: relative;
    padding-left: 6px;
}

.section > ul li:before {
    content: "•";
    position: absolute;
    left: 0;
}

/* Print styles for A4 format */
@media print {
    @page {
        size: A4;
        margin: 12mm;
    }

    body {
        background: white;
        padding: 0;
    }

    .container {
        width: 100%;
        max-width: none;
        min-height: auto;
        box-shadow: none;
        padding: 0;
        margin: 0;
    }

    .header {
        margin-bottom: 15px;
        padding-bottom: 8px;
    }

    .section {
        page-break-inside: avoid;
        break-inside: avoid;
        margin-bottom: 12px;
    }

    .entry {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    /* Optimize font sizes for print */
    .name-section h1 {
        font-size: 18pt;
    }

    .section h2 {
        font-size: 11pt;
    }

    .entry-header h3 {
        font-size: 9pt;
    }

    .date {
        font-size: 8pt;
    }

    .entry li, .entry p, .section > ul li {
        font-size: 8pt;
    }

    .contact-info p {
        font-size: 9pt;
    }
}

